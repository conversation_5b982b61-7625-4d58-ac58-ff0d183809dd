{"version": 3, "sources": ["../src/index.ts", "../src/use-effect-event.tsx"], "sourcesContent": ["export { useEffectEvent } from './use-effect-event';\n", "/* eslint-disable react-hooks/rules-of-hooks */\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport * as React from 'react';\n\ntype AnyFunction = (...args: any[]) => any;\n\n// See https://github.com/webpack/webpack/issues/14814\nconst useReactEffectEvent = (React as any)[' useEffectEvent '.trim().toString()];\nconst useReactInsertionEffect = (React as any)[' useInsertionEffect '.trim().toString()];\n\n/**\n * Designed to approximate the behavior on `experimental_useEffectEvent` as best\n * as possible until its stable release, and back-fill it as a shim as needed.\n */\nexport function useEffectEvent<T extends AnyFunction>(callback?: T): T {\n  if (typeof useReactEffectEvent === 'function') {\n    return useReactEffectEvent(callback);\n  }\n\n  const ref = React.useRef<AnyFunction | undefined>(() => {\n    throw new Error('Cannot call an event handler while rendering.');\n  });\n  // See https://github.com/webpack/webpack/issues/14814\n  if (typeof useReactInsertionEffect === 'function') {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    useLayoutEffect(() => {\n      ref.current = callback;\n    });\n  }\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => ref.current?.(...args)) as T, []);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACCA,qCAAgC;AAChC,YAAuB;AAKvB,IAAM,sBAAuB,MAAc,mBAAmB,KAAK,EAAE,SAAS,CAAC;AAC/E,IAAM,0BAA2B,MAAc,uBAAuB,KAAK,EAAE,SAAS,CAAC;AAMhF,SAAS,eAAsC,UAAiB;AACrE,MAAI,OAAO,wBAAwB,YAAY;AAC7C,WAAO,oBAAoB,QAAQ;AAAA,EACrC;AAEA,QAAM,MAAY,aAAgC,MAAM;AACtD,UAAM,IAAI,MAAM,+CAA+C;AAAA,EACjE,CAAC;AAED,MAAI,OAAO,4BAA4B,YAAY;AACjD,4BAAwB,MAAM;AAC5B,UAAI,UAAU;AAAA,IAChB,CAAC;AAAA,EACH,OAAO;AACL,wDAAgB,MAAM;AACpB,UAAI,UAAU;AAAA,IAChB,CAAC;AAAA,EACH;AAGA,SAAa,cAAQ,MAAO,IAAI,SAAS,IAAI,UAAU,GAAG,IAAI,GAAS,CAAC,CAAC;AAC3E;", "names": []}