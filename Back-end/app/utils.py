import csv
from typing import List, Any
from sqlalchemy.future import select
import sqlalchemy as sa
import sqlalchemy.exc as sae
from random import sample, seed
from time import time
from models import Category, Book
from db import get_db
import os
from datetime import datetime
# async def get_books_pref(categories: list['str']) -> list[Book]:
#     random_books = []
#     try:
#         db_gen = get_db()
#         db = await anext(db_gen)
#         for category in categories:
#             seed(time())
#             result = await db.execute(select(Category).filter(Category.name == category))
#             cate = result.scalars().first()
#             print(cate)
#             if cate.books:
#                 random_books.extend(sample(cate.books, 10))
#
#         return random_books
#     except Exception as e:
#         print("Preferred books not generated: ", e)
#     finally:
#         await db_gen.aclose()


async def add_categories(categories: List[str]):
    db_gen = get_db()
    try:
        # Replace anext with __anext__ for Python 3.8 compatibility
        db = await db_gen.__anext__()
        for category in categories:
            db.add(Category(name=category))
        await db.commit()
    except sae.IntegrityError:
        print("Categories already exist")
    finally:
        await db_gen.aclose()


async def insert_books_from_csv(csv_file_path):
    """
    Read books from CSV file and insert them into the database.
    Skip insertion if there are already 100 or more books in the database.
    """
    db_gen = get_db()
    if not os.path.exists(csv_file_path):
        print(f"Data file `{csv_file_path}` does not exist")
        return
    
    try:
        # Replace anext with __anext__ for Python 3.8 compatibility
        db = await db_gen.__anext__()
        
        # Check if we already have 100+ books
        result = await db.execute(sa.func.count(Book.isbn))
        book_count = result.scalar()
        
        if book_count >= 100:
            print(f"Database already contains {book_count} books. Skipping CSV import.")
            return
            
        # First, get all categories to map names to IDs
        result = await db.execute(select(Category))
        categories = {category.name: category.id for category in result.scalars().all()}
        
        # Read CSV file
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                try:
                    # Get category ID from name
                    category_name = row['Category']
                    if category_name not in categories:
                        # Create category if it doesn't exist
                        new_category = Category(name=category_name)
                        db.add(new_category)
                        await db.commit()
                        await db.refresh(new_category)
                        categories[category_name] = new_category.id
                    
                    # Format data for Book model
                    book_data = {
                        'isbn': row['ISBN'],
                        'title': row['Title'],
                        'author': [author.strip() for author in row['Author'].split(',')],
                        'language': row['Language'],
                        'description': row['Description'],
                        'cover': row['Cover'],
                        'number_of_pages': int(row['Number of page']) if row['Number of page'] else 0,
                        'date_of_publish': datetime.strptime(row['Date of publish'].strip('"'), '%B %d, %Y').date(),
                        'category_id': categories[category_name]
                    }
                    
                    # Add book to database
                    book = Book(**book_data)
                    db.add(book)
                    await db.commit()
                    print(f"Added book: {book_data['title']}")
                    
                except Exception as e:
                    print(f"Error adding book {row.get('Title', 'unknown')}: {e}")
                    await db.rollback()
                    
    except Exception as e:
        print(f"Error processing CSV: {e}")
    finally:
        await db_gen.aclose()




