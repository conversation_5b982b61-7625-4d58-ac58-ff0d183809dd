
from fastapi import API<PERSON><PERSON><PERSON>, Depends, <PERSON><PERSON><PERSON>, HTTPEx<PERSON>, status
from fastapi.security import OAuth2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm
from datetime import timedelta
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import AsyncSession
from typing_extensions import Annotated
from db import get_db
from app import app
from dependencies import get_current_user, get_redis
from auth import password_hash, verify_password, create_access_token, create_refresh_token, verify_token
from config import settings
import schemas
import json
import random
from time import time
#Models
from models import User, Book, Interaction, Category
router = APIRouter()


@app.post('/login', response_model=schemas.GlobalToken, summary='Authenticate user')
async def login(
        form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
        db: AsyncSession = Depends(get_db)
) -> schemas.GlobalToken:
    result = await db.execute(select(User).filter(User.username == form_data.username))
    user = result.scalars().first()
    if not user or not verify_password(form_data.password, user.password):
        raise HTTPException(
            status_code=400,
            detail={'error': "Incorrect username or password", 'connect': False},
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = create_access_token(data={"sub": user.username})
    refresh_token_ = create_refresh_token(data={"sub": user.username})
    return schemas.GlobalToken(access_token=access_token, refresh_token=refresh_token_, type="bearer")


@app.post('/refresh-token')
async def refresh_token(token: schemas.Token) -> schemas.Token:
    payload = verify_token(token.token)
    if payload is None:
        raise HTTPException(status_code=401, detail="Invalid or expired refresh token")
    new_access_token = create_access_token({"sub": payload["sub"]})
    return schemas.Token(token=new_access_token, type='Bearer')


@app.post('/create-user', response_model=schemas.User)
async def create_user(
        user_form: schemas.CreateUser,
        db: AsyncSession = Depends(get_db)
):
    result = await db.execute(select(User).filter(User.username == user_form.username or User.email == user_form.email))
    user = result.scalars().first()
    if user:
        raise HTTPException(
            status_code=400,
            detail='Username or email Already registered'
        )
    hashed_password = password_hash(user_form.password)
    db_user = User(
        username=user_form.username,
        email=user_form.email,
        age=user_form.age,
        password=hashed_password,
        preferred_categories=user_form.preferred_categories
    )
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    return db_user


@app.get('/books', summary='books from different categories')
async def get_books(
        current_user: User = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
) -> schemas.Books:
    books = []
    preferred_categories = current_user.preferred_categories
    for category in preferred_categories:
        random.seed(time())
        result = await db.execute(
            select(Category)
            .options(selectinload(Category.books))
            .filter(Category.name.ilike(category)))
        cate = result.scalars().first()
        print(cate)
        if cate and cate.books:
            books.extend(random.sample(cate.books, 10))
    if not books:
        return schemas.Books(
            number_of_books=0,
            books=[]
        )
    books_result = []
    for book in books:
        book = book.__dict__
        book.pop('_sa_instance_state', None)
        books_result.append(book)
    return schemas.Books(
        number_of_books=len(books_result),
        books=books_result
    )


@app.get('/recommended-books', summary='Recommended books for a special User')
async def get_recommended_books(
        current_user: User = Depends(get_current_user),
        redis_client=Depends(get_redis)
):
    user_id = current_user.id
    key = f"user:{user_id}:books"
    recommended_books = redis_client.hgetall(key)
    if not recommended_books:
        return schemas.RecommendedBooks(
            books=[],
            number_of_books=0
        )
    recommended_books = json.loads(recommended_books)
    return schemas.RecommendedBooks(
        books=recommended_books,
        number_of_books=len(recommended_books)
    )


@app.post('/interaction')
async def interaction(
        interaction_form: schemas.Interaction,
        current_user=Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
):
    interaction_obj = Interaction(
        user_id=current_user.id,
        book_id=interaction_form.book_id,
        action=interaction_form.type
    )
    db.add(interaction_obj)
    await db.commit()
    await db.refresh(interaction_obj)
    return interaction_obj

@app.get('/categories')
async def get_categories(
        db: AsyncSession = Depends(get_db)
) -> schemas.Categories:
    result = await db.execute(select(Category))
    categories = result.scalars().all()
    return schemas.Categories(
        categories={category.name: category.id for category in categories}
    )
