
from db import Base
from datetime import datetime
from typing import List
import sqlalchemy.orm as so
import sqlalchemy as sa


class User(Base):
    __tablename__ = "User"
    id: so.Mapped['int'] = so.mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    username: so.Mapped['str'] = so.mapped_column(sa.String(100), unique=True)
    age: so.Mapped['int'] = so.mapped_column(sa.Integer)
    email: so.Mapped['str'] = so.mapped_column(sa.String(200), unique=True)
    password: so.Mapped['str'] = so.mapped_column(sa.String(256))
    created_at: so.Mapped['datetime'] = so.mapped_column(sa.TIMESTAMP(timezone=False), default=sa.func.now())
    preferred_categories: so.Mapped['List'] = so.mapped_column(sa.ARRAY(sa.String))


class Book(Base):
    __tablename__ = "Book"
    isbn: so.Mapped[str] = so.mapped_column(sa.String(20), primary_key=True)
    title: so.Mapped[str] = so.mapped_column(sa.String(200))
    author: so.Mapped[List] = so.mapped_column(sa.ARRAY(sa.String))
    language: so.Mapped[str] = so.mapped_column(sa.String(20))
    description: so.Mapped[str] = so.mapped_column(sa.TEXT)
    cover: so.Mapped[str] = so.mapped_column(sa.String(250))
    number_of_pages: so.Mapped[int] = so.mapped_column(sa.Integer)
    date_of_publish: so.Mapped[datetime] = so.mapped_column(sa.DATE)
    category_id: so.Mapped[int] = so.mapped_column(sa.ForeignKey('Category.id'))
    category: so.Mapped['Category'] = so.relationship(back_populates='books')


class Category(Base):
    __tablename__ = "Category"
    id: so.Mapped[int] = so.mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    name: so.Mapped[str] = so.mapped_column(sa.String(100), unique=True)
    books: so.Mapped[List['Book']] = so.relationship(back_populates='category')


class Interaction(Base):
    __tablename__ = "Interaction"
    id: so.Mapped[int] = so.mapped_column(sa.Integer, primary_key=True, autoincrement=True)
    user_id: so.Mapped[int] = so.mapped_column(sa.Integer, sa.ForeignKey('User.id'))
    book_id: so.Mapped[str] = so.mapped_column(sa.String(20), sa.ForeignKey('Book.isbn'))
    action: so.Mapped[int] = so.mapped_column(sa.String, sa.CheckConstraint("action IN ('like', 'view')"))
    action_date: so.Mapped[datetime] = so.mapped_column(sa.TIMESTAMP(timezone=False), default=sa.func.now())




