from pydantic import BaseModel, Field
from typing import Literal, Any, Optional, List, Dict
from datetime import datetime


class GlobalToken(BaseModel):
    access_token: str = Field(..., description='JWT access Token')
    refresh_token: str = Field(..., description='JWT refresh Token')
    type: str = Field(..., description='Token type, typically "bearer"')


class Token(BaseModel):
    token: str = Field(..., description='JWT access Token')
    type: str = Field(..., description='Token type, typically "bearer"')

class TokenData(BaseModel):
    username: Optional[str] = None


# Requests
class User(BaseModel):
    username: str = Field(..., description='Unique username')
    email: str = Field(..., description='unique email')
    age: int = Field(..., description='The user age')
    preferred_categories: List = Field(
        ...,
        description='A list of categories preferred'
    )
    status: str = Field(default='Success', description='Present the status')


class CreateUser(BaseModel):
    username: str = Field(..., description='Unique username')
    email: str = Field(..., description='unique email')
    age: int = Field(..., description='The user age')
    password: str = Field(..., description='User password')
    preferred_categories: List[str] = Field(
        ...,
        description='A list of categories preferred'
    )


# Response
class Books(BaseModel):
    number_of_books: int = Field(description='Number of books to return', default=10)
    books: List[Dict[str, Any]] = Field(..., description='A dict contain books information')
    class Config:
        from_attributes = True


class RecommendedBooks(BaseModel):
    books: List[Dict[str, Any]] = Field(..., description="Books recommended for a special user")
    number_of_books: int = Field(description='Number of recommended books returned', default=10)


class Interaction(BaseModel):
    book_id: str = Field(..., description='ISBN of the book')
    type: str = Field(..., description='Type of interaction (like, view)')
    timing: datetime = Field(..., description='When the interaction occurred')


class Categories(BaseModel):
    categories: Dict[str, int]

