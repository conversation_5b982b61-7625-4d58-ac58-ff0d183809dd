from fastapi import FastAPI
from db import Base, engine
from utils import add_categories, insert_books_from_csv
app = FastAPI(
    title="Book Recommendation System"
)

from routes import router
app.include_router(router)


@app.on_event("startup")
async def on_startup():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Add categories and books
    # await add_categories([
    #     'computer-science', 'comics', 'math', 'business',
    #     'economics', 'artificial-intelligence', 'politics', 'studio-ghibli',
    #     'self-help', 'technology', 'personal-development',
    #     'nonfiction', 'children', 'history', 'biology', 'cars', 'fantasy',
    #     ]
    # )
    # Replace with the actual path to your CSV file
    await insert_books_from_csv("/home/<USER>/Bureau/reco-pipeline-back-end/books_dataset0.csv")


